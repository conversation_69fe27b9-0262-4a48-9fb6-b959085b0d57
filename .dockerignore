# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.pnpm-debug.log*

# Local env files
.env*.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Next.js
.next/
out/

# Production
build

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local development
.vscode/
.idea/

# Testing
coverage/
.nyc_output

# Storybook
.storybook/
storybook-static/

# Database
*.db
*.db-journal
dev.db*

# Git
.git
.gitignore
README.md

# Docker
Dockerfile*
docker-compose*
.dockerignore
