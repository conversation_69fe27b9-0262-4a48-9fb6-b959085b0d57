'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'

interface ActivationKey {
  id: string
  username: string
  email: string
  key: string
  isActive: boolean
  expiresAt: string
  createdAt: string
}

interface KeysTableProps {
  refreshTrigger: number
}

export function KeysTable({ refreshTrigger }: KeysTableProps) {
  const [keys, setKeys] = useState<ActivationKey[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [keyToDelete, setKeyToDelete] = useState<ActivationKey | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  const fetchKeys = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/keys')

      if (!response.ok) {
        throw new Error('Failed to fetch keys')
      }

      const data = await response.json()
      setKeys(data.keys)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchKeys()
  }, [refreshTrigger])

  const toggleKeyStatus = async (id: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/keys/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive: !currentStatus }),
      })

      if (!response.ok) {
        throw new Error('Failed to update key status')
      }

      // Refresh the keys list
      fetchKeys()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    }
  }

  const handleDeleteClick = (key: ActivationKey) => {
    setKeyToDelete(key)
    setDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    if (!keyToDelete) return

    try {
      setIsDeleting(true)
      const response = await fetch(`/api/keys/${keyToDelete.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete key')
      }

      // Refresh the keys list
      fetchKeys()
      setDeleteDialogOpen(false)
      setKeyToDelete(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false)
    setKeyToDelete(null)
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
    } catch (err) {
      console.error('Failed to copy to clipboard:', err)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date()
  }

  if (isLoading) {
    return <div className="text-center py-4">Loading keys...</div>
  }

  if (error) {
    return <div className="text-red-600 text-center py-4">{error}</div>
  }

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold">Activation Keys</h2>

      {keys.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          No activation keys found. Create your first key above.
        </div>
      ) : (
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Username</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Key</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Expires At</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {keys.map((key) => (
                <TableRow key={key.id}>
                  <TableCell className="font-medium">{key.username}</TableCell>
                  <TableCell>{key.email}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                        {key.key.slice(0, 8)}...
                      </code>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(key.key)}
                      >
                        Copy
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-1">
                      <Badge variant={key.isActive ? 'success' : 'secondary'}>
                        {key.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                      {isExpired(key.expiresAt) && (
                        <Badge variant="destructive">Expired</Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{formatDate(key.expiresAt)}</TableCell>
                  <TableCell>{formatDate(key.createdAt)}</TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toggleKeyStatus(key.id, key.isActive)}
                      >
                        {key.isActive ? 'Deactivate' : 'Activate'}
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteClick(key)}
                      >
                        Delete
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Activation Key</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this activation key? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          {keyToDelete && (
            <div className="py-4">
              <div className="space-y-2 text-sm">
                <div><strong>Username:</strong> {keyToDelete.username}</div>
                <div><strong>Email:</strong> {keyToDelete.email}</div>
                <div><strong>Key:</strong> <code className="bg-gray-100 px-1 py-0.5 rounded">{keyToDelete.key}</code></div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={handleDeleteCancel}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
