'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'

interface ValidationResult {
  valid: boolean
  reason?: string
  key?: {
    username: string
    email: string
    expiresAt: string
    createdAt: string
  }
}

export function KeyValidator() {
  const [keyToValidate, setKeyToValidate] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<ValidationResult | null>(null)
  const [error, setError] = useState('')

  const validateKey = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!keyToValidate.trim()) {
      setError('Please enter a key to validate')
      return
    }

    setIsLoading(true)
    setError('')
    setResult(null)

    try {
      const response = await fetch(`/api/keys/validate/${encodeURIComponent(keyToValidate)}`)
      const data = await response.json()

      if (response.ok) {
        setResult(data)
      } else {
        setResult(data)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Validate Activation Key</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <form onSubmit={validateKey} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="key">Activation Key</Label>
            <Input
              id="key"
              type="text"
              value={keyToValidate}
              onChange={(e) => setKeyToValidate(e.target.value)}
              placeholder="Enter activation key (UUID)"
              required
            />
          </div>

          {error && (
            <div className="text-red-600 text-sm">{error}</div>
          )}

          <Button type="submit" disabled={isLoading} className="w-full">
            {isLoading ? 'Validating...' : 'Validate Key'}
          </Button>
        </form>

        {result && (
          <div className="mt-6 p-4 border rounded-lg">
            <div className="flex items-center space-x-2 mb-4">
              <Badge variant={result.valid ? 'success' : 'destructive'}>
                {result.valid ? 'Valid' : 'Invalid'}
              </Badge>
              {!result.valid && result.reason && (
                <span className="text-sm text-gray-600">
                  Reason: {result.reason}
                </span>
              )}
            </div>

            {result.key && (
              <div className="space-y-2 text-sm">
                <div>
                  <strong>Username:</strong> {result.key.username}
                </div>
                <div>
                  <strong>Email:</strong> {result.key.email}
                </div>
                <div>
                  <strong>Expires At:</strong> {formatDate(result.key.expiresAt)}
                </div>
                <div>
                  <strong>Created At:</strong> {formatDate(result.key.createdAt)}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
