'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface KeyFormProps {
  onKeyCreated: () => void
}

type ExpirationOption = '1month' | '2months' | '3months' | '6months' | '1year' | '2years' | '3years' | 'custom'

export function KeyForm({ onKeyCreated }: KeyFormProps) {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    expiresAt: '',
  })
  const [expirationOption, setExpirationOption] = useState<ExpirationOption>('1month')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      // Calculate expiration date based on selected option
      const expirationDate = getExpirationDate()

      const response = await fetch('/api/keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          expiresAt: expirationDate,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create key')
      }

      // Reset form
      setFormData({
        username: '',
        email: '',
        expiresAt: '',
      })
      setExpirationOption('1month')

      onKeyCreated()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value,
    }))
  }

  const handleExpirationOptionChange = (value: ExpirationOption) => {
    setExpirationOption(value)
    // Clear custom date when switching away from custom
    if (value !== 'custom') {
      setFormData(prev => ({ ...prev, expiresAt: '' }))
    }
  }

  // Calculate expiration date based on selected option
  const getExpirationDate = () => {
    if (expirationOption === 'custom') {
      return formData.expiresAt
    }

    const now = new Date()
    switch (expirationOption) {
      case '1month':
        now.setMonth(now.getMonth() + 1)
        break
      case '2months':
        now.setMonth(now.getMonth() + 2)
        break
      case '3months':
        now.setMonth(now.getMonth() + 3)
        break
      case '6months':
        now.setMonth(now.getMonth() + 6)
        break
      case '1year':
        now.setFullYear(now.getFullYear() + 1)
        break
      case '2years':
        now.setFullYear(now.getFullYear() + 2)
        break
      case '3years':
        now.setFullYear(now.getFullYear() + 3)
        break
    }
    return now.toISOString()
  }

  // Get display text for current expiration
  const getExpirationDisplay = () => {
    if (expirationOption === 'custom' && formData.expiresAt) {
      return new Date(formData.expiresAt).toLocaleString()
    }

    const date = new Date()
    switch (expirationOption) {
      case '1month':
        date.setMonth(date.getMonth() + 1)
        return date.toLocaleDateString()
      case '2months':
        date.setMonth(date.getMonth() + 2)
        return date.toLocaleDateString()
      case '3months':
        date.setMonth(date.getMonth() + 3)
        return date.toLocaleDateString()
      case '6months':
        date.setMonth(date.getMonth() + 6)
        return date.toLocaleDateString()
      case '1year':
        date.setFullYear(date.getFullYear() + 1)
        return date.toLocaleDateString()
      case '2years':
        date.setFullYear(date.getFullYear() + 2)
        return date.toLocaleDateString()
      case '3years':
        date.setFullYear(date.getFullYear() + 3)
        return date.toLocaleDateString()
      default:
        return ''
    }
  }

  // Set default expiration to 30 days from now for custom option
  const getDefaultExpiration = () => {
    const date = new Date()
    date.setDate(date.getDate() + 30)
    return date.toISOString().slice(0, 16) // Format for datetime-local input
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Create New Activation Key</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="username">Username</Label>
            <Input
              id="username"
              name="username"
              type="text"
              value={formData.username}
              onChange={handleChange}
              required
              placeholder="Enter username"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              required
              placeholder="Enter email address"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="expirationOption">Expiration Period</Label>
            <Select value={expirationOption} onValueChange={handleExpirationOptionChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select expiration period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1month">1 Month</SelectItem>
                <SelectItem value="2months">2 Months</SelectItem>
                <SelectItem value="3months">3 Months</SelectItem>
                <SelectItem value="6months">6 Months</SelectItem>
                <SelectItem value="1year">1 Year</SelectItem>
                <SelectItem value="2years">2 Years</SelectItem>
                <SelectItem value="3years">3 Years</SelectItem>
                <SelectItem value="custom">Custom Date</SelectItem>
              </SelectContent>
            </Select>

            {expirationOption !== 'custom' && (
              <div className="text-sm text-gray-600">
                Will expire on: {getExpirationDisplay()}
              </div>
            )}
          </div>

          {expirationOption === 'custom' && (
            <div className="space-y-2">
              <Label htmlFor="expiresAt">Custom Expiration Date</Label>
              <Input
                id="expiresAt"
                name="expiresAt"
                type="datetime-local"
                value={formData.expiresAt || getDefaultExpiration()}
                onChange={handleChange}
                required
              />
            </div>
          )}

          {error && (
            <div className="text-red-600 text-sm">{error}</div>
          )}

          <Button type="submit" disabled={isLoading} className="w-full">
            {isLoading ? 'Creating...' : 'Create Key'}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
