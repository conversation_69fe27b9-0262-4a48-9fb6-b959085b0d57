'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface ActivationKey {
  id: string
  username: string
  email: string
  key: string
  isActive: boolean
  expiresAt: string
  createdAt: string
}

type FilterType = 'all' | 'active' | 'inactive'

interface ActivationKeysTableProps {
  refreshTrigger?: number
}

export function ActivationKeysTable({ refreshTrigger = 0 }: ActivationKeysTableProps) {
  const [allKeys, setAllKeys] = useState<ActivationKey[]>([])
  const [filteredKeys, setFilteredKeys] = useState<ActivationKey[]>([])
  const [filter, setFilter] = useState<FilterType>('all')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [keyToDelete, setKeyToDelete] = useState<ActivationKey | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  const fetchActivationKeys = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/keys')

      if (!response.ok) {
        throw new Error('Failed to fetch keys')
      }

      const data = await response.json()
      setAllKeys(data.keys)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const applyFilter = (keys: ActivationKey[], filterType: FilterType) => {
    switch (filterType) {
      case 'active':
        return keys.filter(key => key.isActive)
      case 'inactive':
        return keys.filter(key => !key.isActive)
      case 'all':
      default:
        return keys
    }
  }

  const handleFilterChange = (newFilter: FilterType) => {
    setFilter(newFilter)
    setFilteredKeys(applyFilter(allKeys, newFilter))
  }

  useEffect(() => {
    fetchActivationKeys()
  }, [refreshTrigger])

  useEffect(() => {
    setFilteredKeys(applyFilter(allKeys, filter))
  }, [allKeys, filter])

  const toggleKeyStatus = async (id: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/keys/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive: !currentStatus }),
      })

      if (!response.ok) {
        throw new Error('Failed to update key status')
      }

      // Refresh the keys list
      fetchActivationKeys()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    }
  }

  const handleDeleteClick = (key: ActivationKey) => {
    setKeyToDelete(key)
    setDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    if (!keyToDelete) return

    try {
      setIsDeleting(true)
      const response = await fetch(`/api/keys/${keyToDelete.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete key')
      }

      // Refresh the keys list
      fetchActivationKeys()
      setDeleteDialogOpen(false)
      setKeyToDelete(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false)
    setKeyToDelete(null)
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
    } catch (err) {
      console.error('Failed to copy to clipboard:', err)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date()
  }

  const getStatusCounts = () => {
    const activeCount = allKeys.filter(key => key.isActive).length
    const inactiveCount = allKeys.filter(key => !key.isActive).length
    return { activeCount, inactiveCount, totalCount: allKeys.length }
  }

  const { activeCount, inactiveCount, totalCount } = getStatusCounts()

  if (isLoading) {
    return <div className="text-center py-4">Loading activation keys...</div>
  }

  if (error) {
    return <div className="text-red-600 text-center py-4">{error}</div>
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Activation Keys</h2>
        <div className="text-sm text-gray-600">
          {totalCount} total key{totalCount !== 1 ? 's' : ''} 
          ({activeCount} active, {inactiveCount} inactive)
        </div>
      </div>

      <div className="flex items-center space-x-4">
        <label htmlFor="filter" className="text-sm font-medium">
          Filter:
        </label>
        <Select value={filter} onValueChange={handleFilterChange}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Select filter" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Keys ({totalCount})</SelectItem>
            <SelectItem value="active">Active ({activeCount})</SelectItem>
            <SelectItem value="inactive">Inactive ({inactiveCount})</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {filteredKeys.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          {filter === 'all' && 'No activation keys found.'}
          {filter === 'active' && 'No active keys found.'}
          {filter === 'inactive' && 'No inactive keys found.'}
        </div>
      ) : (
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Username</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Key</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Expires At</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredKeys.map((key) => (
                <TableRow key={key.id}>
                  <TableCell className="font-medium">{key.username}</TableCell>
                  <TableCell>{key.email}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                        {key.key.slice(0, 8)}...
                      </code>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(key.key)}
                      >
                        Copy
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-1">
                      <Badge variant={key.isActive ? "success" : "secondary"}>
                        {key.isActive ? "Active" : "Inactive"}
                      </Badge>
                      {isExpired(key.expiresAt) && (
                        <Badge variant="destructive">Expired</Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{formatDate(key.expiresAt)}</TableCell>
                  <TableCell>{formatDate(key.createdAt)}</TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toggleKeyStatus(key.id, key.isActive)}
                      >
                        {key.isActive ? 'Deactivate' : 'Activate'}
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteClick(key)}
                      >
                        Delete
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Activation Key</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this activation key? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          {keyToDelete && (
            <div className="py-4">
              <div className="space-y-2 text-sm">
                <div><strong>Username:</strong> {keyToDelete.username}</div>
                <div><strong>Email:</strong> {keyToDelete.email}</div>
                <div><strong>Key:</strong> <code className="bg-gray-100 px-1 py-0.5 rounded">{keyToDelete.key}</code></div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={handleDeleteCancel}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
