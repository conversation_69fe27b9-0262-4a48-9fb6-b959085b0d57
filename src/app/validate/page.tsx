import { KeyValidator } from '@/components/key-validator'
import { ProtectedPage } from '@/components/protected-page'

export default function ValidatePage() {
  return (
    <ProtectedPage>
      <div className="container mx-auto py-8 px-4 max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-2">Key Validation</h1>
          <p className="text-gray-600">
            Validate activation keys to check their status and expiration
          </p>
        </div>

        <KeyValidator />
      </div>
    </ProtectedPage>
  )
}
