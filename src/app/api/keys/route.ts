import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { v4 as uuidv4 } from 'uuid'
import { getUserFromRequest } from '@/lib/auth'

// GET /api/keys - List all activation keys
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getUserFromRequest(request)
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    const [keys, total] = await Promise.all([
      prisma.activationKey.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      prisma.activationKey.count(),
    ])

    return NextResponse.json({
      keys,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching keys:', error)
    return NextResponse.json(
      { error: 'Failed to fetch keys' },
      { status: 500 }
    )
  }
}

// POST /api/keys - Create new activation key
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getUserFromRequest(request)
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }
    const body = await request.json()
    const { username, email, expiresAt } = body

    if (!username || !email || !expiresAt) {
      return NextResponse.json(
        { error: 'Username, email, and expiration date are required' },
        { status: 400 }
      )
    }

    // Generate UUID for the key
    const key = uuidv4()

    const activationKey = await prisma.activationKey.create({
      data: {
        username,
        email,
        key,
        expiresAt: new Date(expiresAt),
      },
    })

    return NextResponse.json(activationKey, { status: 201 })
  } catch (error) {
    console.error('Error creating key:', error)
    return NextResponse.json(
      { error: 'Failed to create key' },
      { status: 500 }
    )
  }
}
