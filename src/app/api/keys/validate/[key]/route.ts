import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// GET /api/keys/validate/[key] - Validate activation key
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ key: string }> }
) {
  try {
    const { key } = await params

    const activationKey = await prisma.activationKey.findUnique({
      where: { key },
    })

    if (!activationKey) {
      return NextResponse.json(
        {
          valid: false,
          reason: 'Key not found'
        },
        { status: 404 }
      )
    }

    const now = new Date()
    const isExpired = now > activationKey.expiresAt

    if (!activationKey.isActive) {
      return NextResponse.json({
        valid: false,
        reason: 'Key is deactivated',
        key: {
          username: activationKey.username,
          email: activationKey.email,
          expiresAt: activationKey.expiresAt,
          createdAt: activationKey.createdAt,
        },
      })
    }

    if (isExpired) {
      return NextResponse.json({
        valid: false,
        reason: 'Key has expired',
        key: {
          username: activation<PERSON>ey.username,
          email: activationKey.email,
          expiresAt: activationKey.expiresAt,
          createdAt: activationKey.createdAt,
        },
      })
    }

    return NextResponse.json({
      valid: true,
      key: {
        username: activationKey.username,
        email: activationKey.email,
        expiresAt: activationKey.expiresAt,
        createdAt: activationKey.createdAt,
      },
    })
  } catch (error) {
    console.error('Error validating key:', error)
    return NextResponse.json(
      { error: 'Failed to validate key' },
      { status: 500 }
    )
  }
}
