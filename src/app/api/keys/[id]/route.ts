import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// PATCH /api/keys/[id] - Toggle key active status
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const { isActive } = body

    if (typeof isActive !== 'boolean') {
      return NextResponse.json(
        { error: 'isActive must be a boolean' },
        { status: 400 }
      )
    }

    const updatedKey = await prisma.activationKey.update({
      where: { id },
      data: { isActive },
    })

    return NextResponse.json(updatedKey)
  } catch (error) {
    console.error('Error updating key:', error)
    return NextResponse.json(
      { error: 'Failed to update key' },
      { status: 500 }
    )
  }
}

// DELETE /api/keys/[id] - Delete key
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    await prisma.activationKey.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Key deleted successfully' })
  } catch (error) {
    console.error('Error deleting key:', error)
    return NextResponse.json(
      { error: 'Failed to delete key' },
      { status: 500 }
    )
  }
}
