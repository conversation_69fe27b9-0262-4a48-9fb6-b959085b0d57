'use client'

import { useState } from 'react'
import { KeyForm } from '@/components/key-form'
import { KeysTable } from '@/components/keys-table'
import { KeyValidator } from '@/components/key-validator'
import { ProtectedPage } from '@/components/protected-page'

export default function Home() {
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const handleKeyCreated = () => {
    setRefreshTrigger(prev => prev + 1)
  }

  return (
    <ProtectedPage>
      <div className="container mx-auto py-8 px-4 space-y-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-2">Key Manager</h1>
          <p className="text-gray-600">
            Create, manage, and validate activation keys
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <KeyForm onKeyCreated={handleKeyCreated} />
          <KeyValidator />
        </div>

        <KeysTable refreshTrigger={refreshTrigger} />
      </div>
    </ProtectedPage>
  )
}
