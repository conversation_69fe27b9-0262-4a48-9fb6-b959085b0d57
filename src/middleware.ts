import { NextRequest, NextResponse } from 'next/server'

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Allow access to login page and static assets
  if (
    pathname === '/login' ||
    pathname.startsWith('/_next') ||
    pathname.startsWith('/favicon') ||
    pathname === '/api/auth/login' ||
    pathname.startsWith('/api/keys/validate') // Keep validation API public
  ) {
    return NextResponse.next()
  }

  // For all other routes, check if user has a valid session token
  const sessionToken = request.cookies.get('session')?.value

  if (!sessionToken) {
    // No session token, redirect to login
    const loginUrl = new URL('/login', request.url)
    return NextResponse.redirect(loginUrl)
  }

  // For API routes (except validation), we'll let the API handle detailed validation
  // For pages, we do a basic token presence check here and let the page components handle the rest
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}
