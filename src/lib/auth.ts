import { NextRequest } from 'next/server'
import { prisma } from './db'

// Environment-based admin authentication
export function authenticateAdmin(username: string, password: string) {
  const adminUsername = process.env.ADMIN_USERNAME || 'admin'
  const adminPassword = process.env.ADMIN_PASSWORD || 'admin'

  if (username === adminUsername && password === adminPassword) {
    return {
      id: 'admin',
      username: adminUsername,
    }
  }

  return null
}

export async function getUserFromRequest(request: NextRequest) {
  const sessionToken = request.cookies.get('session')?.value

  if (!sessionToken) {
    return null
  }

  const session = await prisma.session.findUnique({
    where: { token: sessionToken },
  })

  if (!session) {
    return null
  }

  // Check if session is expired
  if (session.expiresAt < new Date()) {
    // Clean up expired session
    await prisma.session.delete({
      where: { id: session.id },
    })
    return null
  }

  // Return admin user info from environment
  const adminUsername = process.env.ADMIN_USERNAME || 'admin'
  return {
    id: 'admin',
    username: adminUsername,
  }
}


