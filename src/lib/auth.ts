import { NextRequest } from 'next/server'
import { prisma } from './db'

// Simple hash function for passwords (in production, use bcrypt)
export function hashPassword(password: string): string {
  // This is a simple hash for demo purposes
  // In production, use bcrypt or similar
  return Buffer.from(password).toString('base64')
}

export function verifyPassword(password: string, hashedPassword: string): boolean {
  return hashPassword(password) === hashedPassword
}

export async function createUser(username: string, password: string) {
  const hashedPassword = hashPassword(password)
  
  return await prisma.user.create({
    data: {
      username,
      password: hashedPassword,
    },
  })
}

export async function authenticateUser(username: string, password: string) {
  const user = await prisma.user.findUnique({
    where: { username },
  })

  if (!user) {
    return null
  }

  if (!verifyPassword(password, user.password)) {
    return null
  }

  return user
}

export async function getUserFromRequest(request: NextRequest) {
  const sessionToken = request.cookies.get('session')?.value

  if (!sessionToken) {
    return null
  }

  const session = await prisma.session.findUnique({
    where: { token: sessionToken },
    include: { user: true },
  })

  if (!session) {
    return null
  }

  // Check if session is expired
  if (session.expiresAt < new Date()) {
    // Clean up expired session
    await prisma.session.delete({
      where: { id: session.id },
    })
    return null
  }

  return session.user
}
