import { NextResponse } from 'next/server'
import { v4 as uuidv4 } from 'uuid'
import { prisma } from './db'

const SESSION_DURATION = 24 * 60 * 60 * 1000 // 24 hours in milliseconds

export async function createSession(userId: string) {
  const token = uuidv4()
  const expiresAt = new Date(Date.now() + SESSION_DURATION)

  const session = await prisma.session.create({
    data: {
      userId,
      token,
      expiresAt,
    },
  })

  return session
}

export async function deleteSession(token: string) {
  try {
    await prisma.session.delete({
      where: { token },
    })
  } catch (error) {
    // Session might not exist, ignore error
  }
}

export function setSessionCookie(response: NextResponse, token: string) {
  response.cookies.set('session', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: SESSION_DURATION / 1000, // maxAge is in seconds
    path: '/',
  })
}

export function clearSessionCookie(response: NextResponse) {
  response.cookies.set('session', '', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 0,
    path: '/',
  })
}

export async function cleanupExpiredSessions() {
  await prisma.session.deleteMany({
    where: {
      expiresAt: {
        lt: new Date(),
      },
    },
  })
}
