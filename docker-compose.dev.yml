services:
  key-manager-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=file:/app/data/dev.db
      - ADMIN_USERNAME=${ADMIN_USERNAME:-admin}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-admin}
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
      - key_manager_dev_data:/app/data
    restart: unless-stopped
    command: pnpm dev

volumes:
  key_manager_dev_data:
    driver: local
