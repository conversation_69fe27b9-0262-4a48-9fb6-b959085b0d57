services:
  key-manager:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3009:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=file:/app/data/prod.db
    volumes:
      - key_manager_data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/keys"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  key_manager_data:
    driver: local
