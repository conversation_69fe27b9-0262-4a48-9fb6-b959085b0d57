{"name": "key-manager", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:migrate": "node scripts/migrate.js", "db:reset": "node scripts/migrate.js reset", "db:create": "node scripts/migrate.js create", "db:status": "node scripts/migrate.js status", "db:generate": "prisma generate", "db:seed": "node scripts/seed-admin.js", "docker:build": "docker build -t key-manager .", "docker:run": "docker run -p 3000:3000 -v key_manager_data:/app/data key-manager", "docker:dev": "docker-compose -f docker-compose.dev.yml up --build", "docker:prod": "docker-compose up --build", "docker:stop": "docker-compose down", "docker:clean": "docker-compose down -v && docker system prune -f", "docker:migrate": "docker-compose exec key-manager node scripts/docker-migrate.js"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.8.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "next": "15.3.2", "prisma": "^6.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "zod": "^3.25.30"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}