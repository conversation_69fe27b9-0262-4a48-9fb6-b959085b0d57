# 🐳 Docker Setup for Key Manager

This guide explains how to run the Key Manager application using Docker.

## 🚀 Quick Start

### Production (Recommended)

```bash
# Set admin credentials (required)
export ADMIN_USERNAME=admin
export ADMIN_PASSWORD=your-secure-password

# Build and run with docker-compose
pnpm run docker:prod

# Or manually
docker-compose up --build
```

### Development

```bash
# Run development environment
pnpm run docker:dev

# Or manually
docker-compose -f docker-compose.dev.yml up --build
```

## 📋 Available Commands

```bash
# Build Docker image
pnpm run docker:build

# Run production container
pnpm run docker:run

# Start development environment
pnpm run docker:dev

# Start production environment
pnpm run docker:prod

# Stop containers
pnpm run docker:stop

# Clean up containers and volumes
pnpm run docker:clean
```

## 🗄️ Database Persistence

The SQLite database is stored in a Docker volume to persist data between container restarts:

- **Production**: `key_manager_data` volume → `/app/data/prod.db`
- **Development**: `key_manager_dev_data` volume → `/app/data/dev.db`

## 🔧 Environment Variables

### Required
- `ADMIN_USERNAME` - Admin username (default: admin)
- `ADMIN_PASSWORD` - Admin password (default: admin)

### Production
- `NODE_ENV=production`
- `DATABASE_URL=file:/app/data/prod.db`
- `PORT=3009`

### Development
- `NODE_ENV=development`
- `DATABASE_URL=file:/app/data/dev.db`

### Using Environment File

Create a `.env` file for Docker Compose:

```bash
# Copy example file
cp docker.env.example .env

# Edit with your credentials
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password
```

## 📁 File Structure

```
├── Dockerfile              # Production build
├── Dockerfile.dev          # Development build
├── docker-compose.yml      # Production compose
├── docker-compose.dev.yml  # Development compose
├── .dockerignore           # Docker ignore rules
└── scripts/
    └── docker-entrypoint.sh # Database initialization
```

## 🏥 Health Checks

The production container includes health checks that verify the API is responding:

```bash
# Check container health
docker ps

# View health check logs
docker inspect key-manager_key-manager_1 | grep Health -A 10
```

## 🔍 Troubleshooting

### View Logs
```bash
# Production logs
docker-compose logs -f

# Development logs
docker-compose -f docker-compose.dev.yml logs -f
```

### Access Container
```bash
# Access running container
docker exec -it key-manager_key-manager_1 sh

# Check database
docker exec -it key-manager_key-manager_1 ls -la /app/data/
```

### Reset Everything
```bash
# Stop and remove everything
pnpm run docker:clean

# Rebuild from scratch
pnpm run docker:prod
```

## 🌐 Access Application

Once running, access the application at:
- **Production**: http://localhost:3000
- **Development**: http://localhost:3000 (with hot reload)

## 📊 Container Resources

The containers are optimized for:
- **Image Size**: ~150MB (production)
- **Memory**: ~100MB runtime
- **CPU**: Minimal usage
- **Storage**: Database grows as needed
