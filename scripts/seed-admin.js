const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// Simple hash function for passwords (same as in auth.ts)
function hashPassword(password) {
  return Buffer.from(password).toString('base64')
}

async function seedAdmin() {
  try {
    console.log('🌱 Seeding admin user...')
    
    // Check if admin user already exists
    const existingAdmin = await prisma.user.findUnique({
      where: { username: 'admin' }
    })
    
    if (existingAdmin) {
      console.log('✅ Admin user already exists')
      return
    }
    
    // Create admin user
    const adminUser = await prisma.user.create({
      data: {
        username: 'admin',
        password: hashPassword('admin'),
      },
    })
    
    console.log('✅ Admin user created successfully!')
    console.log('   Username: admin')
    console.log('   Password: admin')
    console.log('   ID:', adminUser.id)
    
  } catch (error) {
    console.error('❌ Error seeding admin user:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

seedAdmin()
