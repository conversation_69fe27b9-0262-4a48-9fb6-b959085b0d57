#!/usr/bin/env node

/**
 * Database Migration Script for Key Manager
 * Cross-platform Node.js script for handling database migrations
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
};

// Utility functions for colored output
const print = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
};

// Check if we're running in Docker
function isDocker() {
  try {
    return fs.existsSync('/.dockerenv') || 
           fs.readFileSync('/proc/1/cgroup', 'utf8').includes('docker');
  } catch {
    return false;
  }
}

// Find Prisma CLI
function findPrismaCmd() {
  try {
    execSync('npx --version', { stdio: 'ignore' });
    return 'npx prisma';
  } catch {
    const localPrisma = path.join(process.cwd(), 'node_modules', '.bin', 'prisma');
    if (fs.existsSync(localPrisma)) {
      return process.platform === 'win32' ? `"${localPrisma}.cmd"` : localPrisma;
    }
    throw new Error('Prisma CLI not found. Please install dependencies first.');
  }
}

// Execute command with proper error handling
function execCommand(command, options = {}) {
  try {
    print.info(`Executing: ${command}`);
    const result = execSync(command, { 
      stdio: 'inherit', 
      encoding: 'utf8',
      ...options 
    });
    return result;
  } catch (error) {
    print.error(`Command failed: ${command}`);
    throw error;
  }
}

// Run database migrations
async function runMigrations(prismaCmd) {
  print.info('Running database migrations...');
  
  try {
    // Generate Prisma client first
    print.info('Generating Prisma client...');
    execCommand(`${prismaCmd} generate`);
    
    // Check if migrations directory exists
    const migrationsDir = path.join(process.cwd(), 'prisma', 'migrations');
    
    if (!fs.existsSync(migrationsDir) || fs.readdirSync(migrationsDir).length === 0) {
      print.info('No existing migrations found. Creating initial migration...');
      execCommand(`${prismaCmd} migrate dev --name init`);
    } else {
      print.info('Deploying pending migrations...');
      execCommand(`${prismaCmd} migrate deploy`);
    }
    
    print.success('Database migrations completed successfully!');
  } catch (error) {
    print.error('Migration failed');
    process.exit(1);
  }
}

// Reset database
async function resetDatabase(prismaCmd) {
  print.warning('This will reset the database and lose all data!');
  
  // Simple prompt for confirmation
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  return new Promise((resolve) => {
    readline.question('Are you sure you want to continue? (y/N): ', (answer) => {
      readline.close();
      
      if (answer.toLowerCase() === 'y') {
        try {
          print.info('Resetting database...');
          execCommand(`${prismaCmd} migrate reset --force`);
          print.success('Database reset completed!');
        } catch (error) {
          print.error('Database reset failed');
          process.exit(1);
        }
      } else {
        print.info('Database reset cancelled.');
      }
      resolve();
    });
  });
}

// Create new migration
async function createMigration(prismaCmd, migrationName) {
  if (!migrationName) {
    print.error('Migration name is required.');
    console.log('Usage: node migrate.js create <migration_name>');
    process.exit(1);
  }
  
  try {
    print.info(`Creating new migration: ${migrationName}`);
    execCommand(`${prismaCmd} migrate dev --name "${migrationName}"`);
    print.success(`Migration '${migrationName}' created successfully!`);
  } catch (error) {
    print.error('Failed to create migration');
    process.exit(1);
  }
}

// Check migration status
async function checkStatus(prismaCmd) {
  try {
    print.info('Checking migration status...');
    execCommand(`${prismaCmd} migrate status`);
  } catch (error) {
    print.error('Failed to check migration status');
    process.exit(1);
  }
}

// Show help
function showHelp() {
  console.log(`
Key Manager Database Migration Script

Usage: node migrate.js [COMMAND] [OPTIONS]

Commands:
  migrate, run     Run pending migrations (default)
  reset           Reset database (WARNING: destroys all data)
  create <name>   Create a new migration
  status          Check migration status
  help            Show this help message

Examples:
  node migrate.js                    # Run migrations
  node migrate.js migrate            # Run migrations
  node migrate.js create add_users   # Create new migration
  node migrate.js status             # Check status
  node migrate.js reset              # Reset database

Environment Variables:
  DATABASE_URL          Database connection string

Docker Usage:
  docker exec key-manager node scripts/migrate.js
  docker-compose exec key-manager node scripts/migrate.js
`);
}

// Main function
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'migrate';
  
  print.info('Key Manager Database Migration Script');
  
  // Check environment
  if (isDocker()) {
    print.info('Running in Docker environment');
  } else {
    print.info('Running in local environment');
  }
  
  // Find Prisma CLI
  let prismaCmd;
  try {
    prismaCmd = findPrismaCmd();
    print.info(`Using Prisma CLI: ${prismaCmd}`);
  } catch (error) {
    print.error(error.message);
    process.exit(1);
  }
  
  // Execute command
  switch (command) {
    case 'migrate':
    case 'run':
      await runMigrations(prismaCmd);
      break;
    case 'reset':
      await resetDatabase(prismaCmd);
      break;
    case 'create':
      await createMigration(prismaCmd, args[1]);
      break;
    case 'status':
      await checkStatus(prismaCmd);
      break;
    case 'help':
    case '-h':
    case '--help':
      showHelp();
      break;
    default:
      print.error(`Unknown command: ${command}`);
      showHelp();
      process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main().catch((error) => {
    print.error(`Script failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { main, runMigrations, resetDatabase, createMigration, checkStatus };
