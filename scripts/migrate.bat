@echo off
REM Database Migration Script for Key Manager (Windows)
REM This script handles database migrations for both local and Docker environments

setlocal enabledelayedexpansion

REM Set colors (if supported)
set "INFO=[INFO]"
set "SUCCESS=[SUCCESS]"
set "WARNING=[WARNING]"
set "ERROR=[ERROR]"

echo %INFO% Key Manager Database Migration Script

REM Function to check if Prisma CLI is available
where npx >nul 2>&1
if %errorlevel% equ 0 (
    set "PRISMA_CMD=npx prisma"
    echo %INFO% Using Prisma CLI: npx prisma
) else (
    if exist ".\node_modules\.bin\prisma.cmd" (
        set "PRISMA_CMD=.\node_modules\.bin\prisma"
        echo %INFO% Using Prisma CLI: .\node_modules\.bin\prisma
    ) else (
        echo %ERROR% Prisma CLI not found. Please install dependencies first.
        exit /b 1
    )
)

REM Parse command line arguments
set "COMMAND=%~1"
if "%COMMAND%"=="" set "COMMAND=migrate"

if "%COMMAND%"=="migrate" goto :run_migrations
if "%COMMAND%"=="run" goto :run_migrations
if "%COMMAND%"=="reset" goto :reset_database
if "%COMMAND%"=="create" goto :create_migration
if "%COMMAND%"=="status" goto :check_status
if "%COMMAND%"=="help" goto :show_help
if "%COMMAND%"=="-h" goto :show_help
if "%COMMAND%"=="--help" goto :show_help

echo %ERROR% Unknown command: %COMMAND%
echo.
goto :show_help

:run_migrations
echo %INFO% Running database migrations...

echo %INFO% Generating Prisma client...
%PRISMA_CMD% generate
if %errorlevel% neq 0 (
    echo %ERROR% Failed to generate Prisma client
    exit /b 1
)

if not exist "prisma\migrations" (
    echo %INFO% No existing migrations found. Creating initial migration...
    %PRISMA_CMD% migrate dev --name init
) else (
    echo %INFO% Deploying pending migrations...
    %PRISMA_CMD% migrate deploy
)

if %errorlevel% equ 0 (
    echo %SUCCESS% Database migrations completed successfully!
) else (
    echo %ERROR% Migration failed
    exit /b 1
)
goto :end

:reset_database
echo %WARNING% This will reset the database and lose all data!
set /p "confirm=Are you sure you want to continue? (y/N): "
if /i "%confirm%"=="y" (
    echo %INFO% Resetting database...
    %PRISMA_CMD% migrate reset --force
    if %errorlevel% equ 0 (
        echo %SUCCESS% Database reset completed!
    ) else (
        echo %ERROR% Database reset failed
        exit /b 1
    )
) else (
    echo %INFO% Database reset cancelled.
)
goto :end

:create_migration
set "MIGRATION_NAME=%~2"
if "%MIGRATION_NAME%"=="" (
    echo %ERROR% Migration name is required.
    echo Usage: %0 create ^<migration_name^>
    exit /b 1
)

echo %INFO% Creating new migration: %MIGRATION_NAME%
%PRISMA_CMD% migrate dev --name "%MIGRATION_NAME%"
if %errorlevel% equ 0 (
    echo %SUCCESS% Migration '%MIGRATION_NAME%' created successfully!
) else (
    echo %ERROR% Failed to create migration
    exit /b 1
)
goto :end

:check_status
echo %INFO% Checking migration status...
%PRISMA_CMD% migrate status
goto :end

:show_help
echo Key Manager Database Migration Script
echo.
echo Usage: %0 [COMMAND] [OPTIONS]
echo.
echo Commands:
echo   migrate, run     Run pending migrations (default)
echo   reset           Reset database (WARNING: destroys all data)
echo   create ^<name^>   Create a new migration
echo   status          Check migration status
echo   help            Show this help message
echo.
echo Examples:
echo   %0                    # Run migrations
echo   %0 migrate            # Run migrations
echo   %0 create add_users   # Create new migration
echo   %0 status             # Check status
echo   %0 reset              # Reset database
echo.
echo Environment Variables:
echo   DATABASE_URL          Database connection string
echo.
echo Docker Usage:
echo   docker exec key-manager ./scripts/migrate.sh
echo   docker-compose exec key-manager ./scripts/migrate.sh
goto :end

:end
endlocal
