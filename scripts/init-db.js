#!/usr/bin/env node

/**
 * Simple Database Initialization Script for Docker
 * This script creates the database tables without requiring Prisma CLI
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
};

// Utility functions for colored output
const print = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
};

// SQLite database schema
const createTableSQL = `
CREATE TABLE IF NOT EXISTS "activation_keys" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "username" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "key" TEXT NOT NULL UNIQUE,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "expiresAt" DATETIME NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS "activation_keys_key_key" ON "activation_keys"("key");
CREATE INDEX IF NOT EXISTS "activation_keys_isActive_idx" ON "activation_keys"("isActive");
CREATE INDEX IF NOT EXISTS "activation_keys_expiresAt_idx" ON "activation_keys"("expiresAt");
`;

// Check if we're running in Docker
function isDocker() {
  try {
    return fs.existsSync('/.dockerenv') || 
           fs.readFileSync('/proc/1/cgroup', 'utf8').includes('docker');
  } catch {
    return false;
  }
}

// Initialize database with SQLite
async function initializeDatabase() {
  const dbPath = process.env.DATABASE_URL?.replace('file:', '') || '/app/data/prod.db';
  
  print.info(`Initializing database at: ${dbPath}`);
  
  try {
    // Import sqlite3 dynamically
    let sqlite3;
    try {
      sqlite3 = require('sqlite3');
    } catch (error) {
      print.warning('sqlite3 not available, database will be created when app starts');
      return;
    }
    
    // Ensure directory exists
    const dbDir = path.dirname(dbPath);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
      print.info(`Created directory: ${dbDir}`);
    }
    
    // Create database connection
    const db = new sqlite3.Database(dbPath);
    
    // Execute schema creation
    await new Promise((resolve, reject) => {
      db.exec(createTableSQL, (err) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
    
    // Close database connection
    await new Promise((resolve) => {
      db.close(resolve);
    });
    
    print.success('Database initialized successfully!');
    
  } catch (error) {
    print.error(`Database initialization failed: ${error.message}`);
    // Don't exit with error - let the app handle database creation
    print.warning('App will attempt to create database on first request');
  }
}

// Main function
async function main() {
  print.info('Key Manager Database Initialization Script');
  
  // Check environment
  if (isDocker()) {
    print.info('Running in Docker environment');
  } else {
    print.info('Running in local environment');
  }
  
  await initializeDatabase();
}

// Run the script
if (require.main === module) {
  main().catch((error) => {
    print.error(`Script failed: ${error.message}`);
    // Don't exit with error code - this is optional initialization
    process.exit(0);
  });
}

module.exports = { main, initializeDatabase };
