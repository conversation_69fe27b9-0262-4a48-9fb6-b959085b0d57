#!/bin/bash

# Database Migration Script for Key Manager
# This script handles database migrations for both local and Docker environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if we're running in Docker
is_docker() {
    [ -f /.dockerenv ] || grep -q 'docker\|lxc' /proc/1/cgroup 2>/dev/null
}

# Function to check if Prisma CLI is available
check_prisma() {
    if command -v npx >/dev/null 2>&1; then
        PRISMA_CMD="npx prisma"
    elif [ -f "./node_modules/.bin/prisma" ]; then
        PRISMA_CMD="./node_modules/.bin/prisma"
    else
        print_error "Prisma CLI not found. Please install dependencies first."
        exit 1
    fi
}

# Function to run migrations
run_migrations() {
    print_status "Running database migrations..."
    
    # Generate Prisma client first
    print_status "Generating Prisma client..."
    $PRISMA_CMD generate
    
    # Check if this is the first migration
    if [ ! -f "prisma/migrations" ] && [ ! -d "prisma/migrations" ]; then
        print_status "No existing migrations found. Creating initial migration..."
        $PRISMA_CMD migrate dev --name init
    else
        # Deploy pending migrations
        print_status "Deploying pending migrations..."
        $PRISMA_CMD migrate deploy
    fi
    
    print_success "Database migrations completed successfully!"
}

# Function to reset database
reset_database() {
    print_warning "This will reset the database and lose all data!"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Resetting database..."
        $PRISMA_CMD migrate reset --force
        print_success "Database reset completed!"
    else
        print_status "Database reset cancelled."
    fi
}

# Function to create new migration
create_migration() {
    if [ -z "$1" ]; then
        print_error "Migration name is required."
        echo "Usage: $0 create <migration_name>"
        exit 1
    fi
    
    print_status "Creating new migration: $1"
    $PRISMA_CMD migrate dev --name "$1"
    print_success "Migration '$1' created successfully!"
}

# Function to check migration status
check_status() {
    print_status "Checking migration status..."
    $PRISMA_CMD migrate status
}

# Function to show help
show_help() {
    echo "Key Manager Database Migration Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  migrate, run     Run pending migrations (default)"
    echo "  reset           Reset database (WARNING: destroys all data)"
    echo "  create <name>   Create a new migration"
    echo "  status          Check migration status"
    echo "  help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run migrations"
    echo "  $0 migrate            # Run migrations"
    echo "  $0 create add_users   # Create new migration"
    echo "  $0 status             # Check status"
    echo "  $0 reset              # Reset database"
    echo ""
    echo "Environment Variables:"
    echo "  DATABASE_URL          Database connection string"
    echo ""
    echo "Docker Usage:"
    echo "  docker exec key-manager ./scripts/migrate.sh"
    echo "  docker-compose exec key-manager ./scripts/migrate.sh"
}

# Main script logic
main() {
    print_status "Key Manager Database Migration Script"
    
    # Check if we're in Docker
    if is_docker; then
        print_status "Running in Docker environment"
    else
        print_status "Running in local environment"
    fi
    
    # Check Prisma availability
    check_prisma
    print_status "Using Prisma CLI: $PRISMA_CMD"
    
    # Parse command line arguments
    case "${1:-migrate}" in
        "migrate"|"run"|"")
            run_migrations
            ;;
        "reset")
            reset_database
            ;;
        "create")
            create_migration "$2"
            ;;
        "status")
            check_status
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
