#!/bin/sh

# Exit on any error
set -e

echo "🚀 Starting Key Manager application..."

# Ensure data directory has correct permissions
chown -R nextjs:nodejs /app/data

# Initialize database
echo "📦 Initializing database..."
su-exec nextjs node scripts/docker-migrate.js

echo "🎯 Starting Next.js application..."
echo "💡 For migrations, use: docker-compose exec key-manager node scripts/docker-migrate.js"
# Switch to nextjs user and start the application
exec su-exec nextjs "$@"
