# Key Manager

A secure activation key management system built with Next.js, Prisma, and SQLite. This application allows you to create, manage, and validate activation keys with built-in authentication.

## Features

- 🔐 **Authentication System**: Secure login/logout with session management
- 🔑 **Key Management**: Create, view, activate/deactivate, and delete activation keys
- ✅ **Key Validation**: Public API endpoint for validating activation keys
- 📊 **Dashboard**: Overview of all keys with filtering and management options
- 🐳 **Docker Support**: Containerized deployment ready
- 💾 **SQLite Database**: Lightweight database with Prisma ORM

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, pnpm, or bun

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Set up the database:

```bash
npm run db:migrate
npm run db:seed
```

4. Start the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

5. Open [http://localhost:3000](http://localhost:3000) with your browser

### Default Login Credentials

- **Username**: `admin`
- **Password**: `admin`

## Authentication

The application includes a complete authentication system:

- **Session-based authentication** with HTTP-only cookies
- **Protected routes** - all pages except login require authentication
- **Public API** - Key validation endpoint (`/api/keys/validate/[key]`) remains public
- **Secure logout** with session cleanup

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Key Management (Protected)
- `GET /api/keys` - List all activation keys
- `POST /api/keys` - Create new activation key
- `PATCH /api/keys/[id]` - Update key status
- `DELETE /api/keys/[id]` - Delete key

### Key Validation (Public)
- `GET /api/keys/validate/[key]` - Validate activation key

## Database Commands

```bash
npm run db:migrate    # Run database migrations
npm run db:seed      # Create admin user
npm run db:reset     # Reset database
npm run db:status    # Check migration status
```

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
