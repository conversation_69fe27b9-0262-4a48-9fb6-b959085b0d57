# 🗄️ Database Migrations Guide

This guide explains how to manage database migrations for the Key Manager application.

## 📋 Available Migration Scripts

We provide multiple migration scripts for different environments and preferences:

### 1. **Node.js <PERSON>ript (Recommended - Cross-platform)**
```bash
node scripts/migrate.js [command]
```

### 2. **Shell Script (Linux/macOS)**
```bash
./scripts/migrate.sh [command]
```

### 3. **Batch Script (Windows)**
```cmd
scripts\migrate.bat [command]
```

## 🚀 Quick Start

### Run Migrations (Most Common)
```bash
# Using npm scripts (recommended)
pnpm run db:migrate

# Or directly
node scripts/migrate.js
./scripts/migrate.sh
scripts\migrate.bat
```

## 📝 Available Commands

### **migrate** (default)
Run pending database migrations
```bash
pnpm run db:migrate
# or
node scripts/migrate.js migrate
```

### **status**
Check current migration status
```bash
pnpm run db:status
# or
node scripts/migrate.js status
```

### **create**
Create a new migration
```bash
pnpm run db:create add_user_roles
# or
node scripts/migrate.js create add_user_roles
```

### **reset**
Reset database (⚠️ **WARNING: Destroys all data!**)
```bash
pnpm run db:reset
# or
node scripts/migrate.js reset
```

### **generate**
Generate Prisma client
```bash
pnpm run db:generate
# or
npx prisma generate
```

## 🐳 Docker Environment

### Run Migrations in Docker Container
```bash
# If container is running
pnpm run docker:migrate
# or
docker-compose exec key-manager node scripts/migrate.js

# If container is not running
docker-compose run --rm key-manager node scripts/migrate.js
```

### Docker Migration Examples
```bash
# Run migrations
docker-compose exec key-manager node scripts/migrate.js

# Check status
docker-compose exec key-manager node scripts/migrate.js status

# Create new migration
docker-compose exec key-manager node scripts/migrate.js create add_feature

# Reset database (careful!)
docker-compose exec key-manager node scripts/migrate.js reset
```

## 🔄 Common Migration Workflows

### **Initial Setup (New Installation)**
```bash
# 1. Install dependencies
pnpm install

# 2. Run initial migration
pnpm run db:migrate

# 3. Start application
pnpm run dev
```

### **Adding New Features**
```bash
# 1. Modify prisma/schema.prisma
# 2. Create migration
pnpm run db:create add_new_feature

# 3. Apply migration
pnpm run db:migrate
```

### **Production Deployment**
```bash
# 1. Build application
pnpm run build

# 2. Run migrations
pnpm run db:migrate

# 3. Start production server
pnpm run start
```

### **Development Reset**
```bash
# Reset database and start fresh
pnpm run db:reset

# Or manually
rm -f prisma/dev.db
pnpm run db:migrate
```

## 🔍 Troubleshooting

### **Migration Failed**
```bash
# Check current status
pnpm run db:status

# Reset and try again
pnpm run db:reset
pnpm run db:migrate
```

### **Prisma Client Out of Sync**
```bash
# Regenerate client
pnpm run db:generate

# Or reset everything
pnpm run db:reset
```

### **Docker Database Issues**
```bash
# Check container logs
docker-compose logs key-manager

# Access container shell
docker-compose exec key-manager sh

# Check database file
docker-compose exec key-manager ls -la /app/data/
```

### **Permission Issues (Docker)**
```bash
# Fix volume permissions
docker-compose down
docker volume rm key_manager_data
docker-compose up --build
```

## 📁 File Structure

```
scripts/
├── migrate.js      # Node.js migration script (cross-platform)
├── migrate.sh      # Shell script (Linux/macOS)
└── migrate.bat     # Batch script (Windows)

prisma/
├── schema.prisma   # Database schema
├── migrations/     # Migration files
└── dev.db         # SQLite database (development)
```

## 🔧 Environment Variables

### **Local Development**
```bash
DATABASE_URL="file:./dev.db"
```

### **Docker Production**
```bash
DATABASE_URL="file:/app/data/prod.db"
```

### **Custom Database Location**
```bash
DATABASE_URL="file:/path/to/your/database.db"
```

## 📊 Migration Best Practices

### **1. Always Backup Before Reset**
```bash
# Backup database
cp prisma/dev.db prisma/dev.db.backup

# Or for Docker
docker cp container_name:/app/data/prod.db ./backup.db
```

### **2. Test Migrations Locally First**
```bash
# Test in development
pnpm run db:migrate

# Then apply to production
```

### **3. Use Descriptive Migration Names**
```bash
# Good
pnpm run db:create add_user_authentication
pnpm run db:create fix_email_validation

# Bad
pnpm run db:create update
pnpm run db:create fix
```

### **4. Check Status Before Deploying**
```bash
pnpm run db:status
```

## 🚨 Important Notes

- **Always backup your database** before running migrations in production
- **Test migrations locally** before applying to production
- **Use `db:reset` carefully** - it destroys all data
- **Check migration status** if you encounter issues
- **Docker volumes persist data** between container restarts
