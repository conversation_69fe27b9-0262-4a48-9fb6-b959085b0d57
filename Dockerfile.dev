# Development Dockerfile
FROM node:18-alpine

# Install dependencies
RUN apk add --no-cache libc6-compat curl

WORKDIR /app

# Enable pnpm
RUN corepack enable pnpm

# Copy package files
COPY package.json pnpm-lock.yaml* ./

# Install dependencies
RUN pnpm install

# Copy source code
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Create data directory for SQLite database
RUN mkdir -p /app/data

# Expose port
EXPOSE 3000

# Start development server
CMD ["pnpm", "dev"]
